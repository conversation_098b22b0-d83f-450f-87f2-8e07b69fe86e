import { useMemo, useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Tabs, Checkbox, Popover, Select, Tag, Tooltip, Avatar } from 'antd';
import { CaretDownOutlined, UserOutlined } from '@ant-design/icons';
import { isEmpty } from 'lodash';
import { useNavigate, useLocation } from 'umi';
import { stringifyUrl } from 'query-string';
import { connectModel } from 'COMMON/middleware';
import baseModel from 'COMMON/models/baseModel';
import commonModel from 'COMMON/models/commonModel';
import { getUserList } from 'COMMON/api/base/common';
import { updateFilterDataFromUrl, getQueryStringFromFilters } from './utils';
import { getQueryParams } from 'COMMON/utils/utils';
import {
    convertTagColorTypeToHexadecimal,
    convertOsTypeToType
} from 'PACKAGES/react-kityminder-editor-v2/src/utils';
import { FILTER_INFO } from './const';
import styles from './Filter.module.less';

const getLinkDescList = (tree, linkDescList = []) => {
    tree.forEach((element) => {
        if (!isEmpty(element?.extra?.linkList)) {
            for (let link of element?.extra?.linkList) {
                if (!linkDescList?.includes(link?.name)) {
                    linkDescList.push(link?.name);
                }
            }
        }
        getLinkDescList(element?.children ?? [], linkDescList);
    });
    return linkDescList;
};
const VALUE_TYPE = {
    2: 'ITP',
    3: 'FastEeb',
    4: 'Monster',
    5: 'ICode',
    6: 'UBC',
    7: 'ICafe'
};

const getRelationList = (tree, osType, relationList = []) => {
    tree.forEach((element) => {
        if (!isEmpty(element?.extra?.relationInfo?.[convertOsTypeToType(osType)])) {
            for (let link of element?.extra?.relationInfo?.[convertOsTypeToType(osType)]) {
                // 同时检查address和platform
                if (
                    !relationList?.find((item) => {
                        item?.address === link?.address && item?.platform === link?.platform;
                    })
                ) {
                    relationList.push(link);
                }
            }
        }
        getRelationList(element?.children ?? [], osType, relationList);
    });
    return relationList;
};

const Filter = (props, ref) => {
    const {
        children,
        type = 'filter',
        caseNode,
        username,
        title,
        tagList,
        filterData,
        handleFilterData,
        disabled = false,
        osType,
        clearFilterData,
        handleSearchFilterData,
        hiddenNoBelong = false
    } = props;
    const [open, setOpen] = useState(false);
    // 获取用户列表
    const [userList, setUserList] = useState([]);
    const query = getQueryParams();
    const navigate = useNavigate();
    const location = useLocation();

    // 构建查询参数
    const queryParams = Object.fromEntries(
        // 过滤 query 中的 filters 字段，保留其他字段
        Object.entries(query).filter(([key]) => !/\bfilters\[[^\]]+\]/.test(key))
    );

    // 通过 ref 将 show 函数传递到父亲组件
    useImperativeHandle(
        ref,
        () => {
            return {
                show: showModal
            };
        },
        [showModal]
    );

    const showModal = () => {
        if (disabled) {
            return;
        }
        setOpen(true);
    };

    // 标签选项（动态变更）
    const tagOptions = useMemo(() => {
        tagList.sort((a, b) => (a.username === username ? -1 : b.username === username ? 1 : 0));
        return tagList.map((item, index) => {
            const color = convertTagColorTypeToHexadecimal(item.color);
            return {
                key: 'tag' + index,
                value: item.id,
                name: item?.tagName,
                username: item.username,
                label: (
                    <>
                        <div
                            className={styles.tagIcon}
                            style={{
                                backgroundColor: color,
                                border: '#d9d9d9 solid 1px'
                            }}
                        />
                        <div className={styles.tagName}>
                            {item.tagName}
                            <span className={styles.tagCreator}>({item?.username})</span>
                        </div>
                    </>
                )
            };
        });
    }, [tagList]);

    // 链接
    const linkDescOptions = useMemo(() => {
        let options = getLinkDescList(caseNode);
        return options.map((item, index) => ({
            key: 'link_desc' + index,
            value: item,
            label: item
        }));
    }, [caseNode]);

    // 关联平台
    const relationOptions = useMemo(() => {
        let options = getRelationList(caseNode, osType);
        return options.map((item, index) => ({
            key: 'relation_' + index,
            value: '' + VALUE_TYPE?.[item.platform] + '.' + item?.address,
            label: (
                <>
                    <Tag>{VALUE_TYPE?.[item.platform]}</Tag>
                    <Tooltip title={item?.address}>{item?.address}</Tooltip>
                </>
            )
        }));
    }, [caseNode, osType]);

    // 用户列表
    const userListOptions = useMemo(() => {
        return (
            userList?.map((d) => ({
                value: d.username,
                label: d.username,
                avatar: d.avatar,
                name: d.name,
                department: d.department,
                imageUrl: d.imageUrl
            })) ?? []
        );
    }, [userList]);

    const handleSearchCreator = (newValue) => {
        if (newValue) {
            getUserList({ queryName: newValue, searchName: username })
                .then((data) => {
                    setUserList(data?.userInfoList);
                })
                .catch((error) => {
                    console.error('请求错误', error);
                });
        } else {
            setUserList([]);
        }
    };

    // 属于不属于切换时
    const onTabChange = (e, type) => {
        handleFilterData(filterData[type]?.data, type, e ?? 'belong');
    };

    // 清空
    const clearFilter = () => {
        let newFilterData = {};
        Object.keys(filterData)?.map((type) => {
            newFilterData = {
                ...newFilterData,
                [type]: {
                    activeKey: 'belong',
                    data: []
                }
            };
        });
        // 对url进行同步变更
        navigate(
            stringifyUrl({
                url: location.pathname,
                query: queryParams
            })
        );
        setOpen(false);
        clearFilterData && clearFilterData(newFilterData);
    };

    const searchFilterData = () => {
        const queryString = getQueryStringFromFilters(filterData, queryParams);
        // 拼接url
        const url = `${location.pathname}${queryString ? `?${queryString}` : ''}`;
        navigate(url);
        setOpen(false);
        handleSearchFilterData();
    };

    const getOptions = (type) => {
        if (type === 'tag') {
            return tagOptions;
        }
        if (type === 'link') {
            return linkDescOptions;
        }
        if (type === 'relation') {
            return relationOptions;
        }
        return FILTER_INFO[type]?.option;
    };

    useEffect(() => {
        // 避免前几次渲染时curOsType为null的情况
        if (osType === null) {
            return;
        }
        const urlQuery = getQueryParams();
        let newFilterData = updateFilterDataFromUrl(filterData, urlQuery);
        // 特殊处理 iCafe (testone 平台)
        if (urlQuery?.['filters[iCafe]']) {
            let options = getRelationList(caseNode, osType);
            let hasRelation = options?.some(
                (item) => item?.address === urlQuery?.['filters[iCafe]']
            );
            if (hasRelation) {
                newFilterData = {
                    ...newFilterData,
                    relation: {
                        ...newFilterData.relation,
                        data: ['ICafe.' + urlQuery?.['filters[iCafe]']]
                    }
                };
            }
        }
        if (JSON.stringify(newFilterData) !== JSON.stringify(filterData)) {
            clearFilterData(newFilterData);
        }
    }, [osType]);

    return (
        <Popover
            open={open}
            trigger="click"
            onOpenChange={setOpen}
            title={
                <div className={styles.header}>
                    <span className={styles.title}>{title ?? '节点筛选'}</span>
                    <span className={styles.group}>
                        <a className={styles.clear} onClick={clearFilter}>
                            清空
                        </a>
                        {type === 'filter' && (
                            <a className={styles.query} onClick={searchFilterData}>
                                查询
                            </a>
                        )}
                    </span>
                </div>
            }
            content={
                <div className={styles.container}>
                    {Object.keys(filterData)?.map((type, index) => {
                        if (hiddenNoBelong && type === 'link') {
                            return null;
                        }
                        return (
                            <div className={styles.filterItem} key={`${type}-${index}`}>
                                <span className={styles.filterTitle}>
                                    {FILTER_INFO[type]?.name}
                                </span>
                                {FILTER_INFO[type]?.element === 'Select' && (
                                    <Select
                                        popupMatchSelectWidth={false}
                                        allowClear
                                        mode={type === 'tag' ? 'tags' : 'multiple'}
                                        placeholder={FILTER_INFO[type]?.placeholder ?? '请选择'}
                                        suffixIcon={
                                            <span style={{ backgroundColor: '#fff', padding: 5 }}>
                                                {filterData[type]?.activeKey === 'belong' &&
                                                    !isEmpty(filterData[type]?.data) &&
                                                    '属于'}
                                                {filterData[type]?.activeKey !== 'belong' &&
                                                    !isEmpty(filterData[type]?.data) &&
                                                    '不属于'}
                                                <CaretDownOutlined />
                                            </span>
                                        }
                                        filterOption={(input, option) => {
                                            return (option?.username || (option?.name ?? ''))
                                                .toLowerCase()
                                                .includes(input.toLowerCase());
                                        }}
                                        value={filterData[type]?.data}
                                        className={styles.filterSelect}
                                        dropdownRender={(menu) => (
                                            <div>
                                                <Tabs
                                                    centered
                                                    defaultActiveKey={filterData[type]?.activeKey}
                                                    onChange={(e) => onTabChange(e, type)}
                                                >
                                                    <Tabs.TabPane tab="属于" key="belong">
                                                        {menu}
                                                    </Tabs.TabPane>
                                                    {!hiddenNoBelong && (
                                                        <Tabs.TabPane tab="不属于" key="noBelong">
                                                            {menu}
                                                        </Tabs.TabPane>
                                                    )}
                                                </Tabs>
                                            </div>
                                        )}
                                        options={getOptions(type)}
                                        onChange={(value) => {
                                            handleFilterData(value, type);
                                        }}
                                        removeIcon={null}
                                    />
                                )}
                                {FILTER_INFO[type]?.element === 'UserSelect' && (
                                    <Select
                                        popupMatchSelectWidth={false}
                                        showSearch
                                        mode={type === 'tag' ? 'tags' : 'multiple'}
                                        placeholder={FILTER_INFO[type]?.placeholder ?? '请选择'}
                                        suffixIcon={
                                            <span style={{ backgroundColor: '#fff', padding: 5 }}>
                                                {filterData[type]?.activeKey === 'belong' &&
                                                    !isEmpty(filterData[type]?.data) &&
                                                    '属于'}
                                                {filterData[type]?.activeKey !== 'belong' &&
                                                    !isEmpty(filterData[type]?.data) &&
                                                    '不属于'}
                                                <CaretDownOutlined />
                                            </span>
                                        }
                                        onSearch={handleSearchCreator}
                                        value={filterData[type]?.data}
                                        className={styles.filterSelect}
                                        dropdownRender={(menu) => (
                                            <div>
                                                <Tabs
                                                    centered
                                                    defaultActiveKey={filterData[type]?.activeKey}
                                                    onChange={(e) => onTabChange(e, type)}
                                                >
                                                    <Tabs.TabPane tab="属于" key="belong">
                                                        {menu}
                                                    </Tabs.TabPane>
                                                    {!hiddenNoBelong && (
                                                        <Tabs.TabPane tab="不属于" key="noBelong">
                                                            {menu}
                                                        </Tabs.TabPane>
                                                    )}
                                                </Tabs>
                                            </div>
                                        )}
                                        options={userListOptions}
                                        onChange={(value) => {
                                            handleFilterData(value, type);
                                        }}
                                        removeIcon={null}
                                        optionRender={(option) => {
                                            return (
                                                <div
                                                    style={{
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        width: '100%',
                                                        overflow: 'scroll'
                                                    }}
                                                >
                                                    <Avatar
                                                        icon={<UserOutlined />}
                                                        src={option?.data?.imageUrl}
                                                        style={{ marginRight: 8 }}
                                                    />
                                                    <div>
                                                        <div>
                                                            {option?.data?.name
                                                                ? `${option?.data?.name} (${option?.data?.label})`
                                                                : option?.data?.label}
                                                        </div>
                                                        <div
                                                            style={{ fontSize: 12, color: '#888' }}
                                                        >
                                                            {option?.data?.department ||
                                                                '暂无部门信息'}
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        }}
                                    />
                                )}
                                {FILTER_INFO[type]?.element === 'CheckBox' && (
                                    <Checkbox
                                        size="small"
                                        checked={filterData[type]}
                                        className={styles.filterCheckbox}
                                        onChange={(e) => handleFilterData(e.target.checked, type)}
                                    />
                                )}
                            </div>
                        );
                    })}
                </div>
            }
        >
            {children}
        </Popover>
    );
};

export default connectModel([baseModel, commonModel], (state) => ({
    username: state.common.base.username,
    tagList: state.common.base.tagList
}))(forwardRef(Filter));
