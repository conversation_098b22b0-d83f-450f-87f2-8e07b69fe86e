import { Input, Select, But<PERSON>, <PERSON>, Row, Col, Popover, Badge } from 'antd';
import { SearchOutlined, FilterOutlined } from '@ant-design/icons';
import styles from './DocumentSearchFilter.module.less';

const { Option } = Select;

const DocumentSearchFilter = ({
    searchText,
    onSearchChange,
    filters,
    onFilterChange,
    onResetFilters,
    onApplyFilters,
    hasActiveFilters,
    statuses = []
}) => {
    const filterContent = (
        <div className={styles.filterPopoverContent}>
            <Form size="small">
                <Row gutter={[8, 8]} align="middle">
                    <Col span={5}>
                        <span className={styles.filterLabel}>
                            文档ID
                        </span>
                    </Col>
                    <Col span={19}>
                        <Input
                            placeholder="请输入文档ID"
                            value={filters.documentId}
                            onChange={(e) =>
                                onFilterChange(
                                    'documentId',
                                    e.target.value
                                )
                            }
                            allowClear
                        />
                    </Col>
                </Row>
                <Row
                    gutter={[8, 5]}
                    className={styles.filterRowMargin}
                    align="middle"
                >
                    <Col span={5}>
                        <span className={styles.filterLabel}>
                            创建人
                        </span>
                    </Col>
                    <Col span={19}>
                        <Input
                            placeholder="请输入创建人"
                            value={filters.creator}
                            onChange={(e) =>
                                onFilterChange(
                                    'creator',
                                    e.target.value
                                )
                            }
                            allowClear
                        />
                    </Col>
                </Row>

                <Row
                    gutter={[8, 8]}
                    className={styles.filterRowMargin}
                    align="middle"
                >
                    <Col span={5}>
                        <span className={styles.filterLabel}>
                            &nbsp; &nbsp;状态
                        </span>
                    </Col>
                    <Col span={19}>
                        <Select
                            placeholder="请选择状态"
                            value={filters.status}
                            onChange={(value) =>
                                onFilterChange('status', value)
                            }
                            onClear={() =>
                                onFilterChange('status', null)
                            }
                            className={styles.filterSelectWidth}
                            allowClear
                            getPopupContainer={() =>
                                document.body
                            }
                        >
                            {statuses.map((status) => (
                                <Option
                                    key={status.key}
                                    value={status.key}
                                >
                                    {status.value}
                                </Option>
                            ))}
                        </Select>
                    </Col>
                </Row>

                <div className={styles.filterActions}>
                    <Button size="small" onClick={onResetFilters}>
                        重置
                    </Button>
                    <Button
                        type="primary"
                        size="small"
                        onClick={onApplyFilters}
                    >
                        确定
                    </Button>
                </div>
            </Form>
        </div>
    );

    return (
        <div className={styles.searchFilterContainer}>
            {/* 搜索框 */}
            <div className={styles.searchContainer}>
                <Input
                    placeholder="输入文档名称..."
                    value={searchText}
                    onChange={(e) => onSearchChange(e.target.value)}
                    prefix={<SearchOutlined />}
                    className={styles.searchInput}
                />
            </div>

            {/* 筛选按钮 */}
            <Popover
                getPopupContainer={() => document.body}
                content={filterContent}
                title="筛选条件"
                trigger="click"
                placement="right"
                zIndex={1000}
                styles={{
                    content: { maxWidth: '320px' }
                }}
            >
                <Badge
                    dot={hasActiveFilters()}
                    offset={[-2, -2]}
                    style={{ zIndex: 9999 }}
                >
                    <FilterOutlined className={styles.filterIcon} />
                </Badge>
            </Popover>
        </div>
    );
};

export default DocumentSearchFilter;
