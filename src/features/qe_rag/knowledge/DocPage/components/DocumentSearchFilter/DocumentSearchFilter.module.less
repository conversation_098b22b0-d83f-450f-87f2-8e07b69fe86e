.searchFilterContainer {
  display: flex;
  gap: 8px;
  align-items: center;
  width: 100%;
}

.searchContainer {
  flex: 1;
}

.searchInput {
  width: 100%;
}

.filterPopoverContent {
  min-width: 300px;
}

.filterLabel {
  font-size: 14px;
}

.filterRowMargin {
  margin-top: 8px;
}

.filterSelectWidth {
  width: 100%;
}

.filterActions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  margin-top: 16px;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.filterIcon {
  cursor: pointer;
  font-size: 16px;
  color: #777777;
}

// Badge 容器样式，确保小红点不被遮挡
.filterBadgeContainer {
  position: relative;
  display: inline-block;

  // 确保 Badge 的小红点有足够的空间显示
  :global(.ant-badge) {
    display: inline-block;

    // 小红点样式调整
    :global(.ant-badge-dot) {
      position: absolute;
      top: -4px;
      right: -4px;
      z-index: 10;
      box-shadow: 0 0 0 1px #fff;
    }
  }
}
