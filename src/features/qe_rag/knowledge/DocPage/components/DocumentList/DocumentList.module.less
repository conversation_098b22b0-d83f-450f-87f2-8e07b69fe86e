// 基础布局样式
.documentListCard {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.listMainContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.listScrollContainer {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

// 下拉菜单项样式
.dropdownItemDisable {
  color: #faad14;
}

.dropdownItemEnable {
  color: #52c41a;
}

.dropdownItemDelete {
  color: #ff4d4f;
}

.dropdownIconMargin {
  margin-right: 8px;
}

// 分页容器样式
.paginationContainer {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

// 文档ID标签样式
.documentIdTag {
  background-color: #f5f5f5;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}

// Card 样式
.cardHeader {
  padding: 16px 16px 8px 16px;
}

.cardBody {
  padding: 8px 16px 16px 16px;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

// 表头样式
.tableHeader {
  display: flex;
  padding: 8px 20px;
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 500;
  font-size: 14px;
  color: #666;
  position: sticky;
  top: 0;
  z-index: 1;
}

.headerCell {
  &.id {
    width: 40px;
    text-align: center;
  }

  &.title {
    flex: 1;
  }

  &.type {
    width: 60px;
    text-align: center;
  }

  &.status {
    width: 80px;
    text-align: center;
  }

  &.actions {
    width: 40px;
  }
}

// 文档项样式
.documentItem {
  cursor: pointer;
  background-color: transparent;
  color: #333333;
  padding: 5px 20px;
  border-radius: 8px;
  margin-bottom: 4px;
  border: none;
  transition: all 0.2s ease;
  box-shadow: none;

  &:hover {
    background-color: #f5f5f5 !important;
  }

  &.selected {
    background-color: #1677ff;
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(22, 119, 255, 0.3);

    &:hover {
      background-color: #1677ff !important;
    }
  }
}

// 文档项内容布局
.documentItemContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.documentItemLeft {
  flex: 1;
}

.documentItemRight {
  display: flex;
  align-items: center;
  gap: 8px;
}

// 文档标题样式
.documentTitle {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;

  &.normal {
    color: #333333;
    font-weight: 400;
  }

  &.selected {
    color: #ffffff;
    font-weight: 500;
  }
}

// Tag 样式
.documentTypeTag {
  min-width: 50px;
  text-align: center;
  display: inline-block;
}

// 更多操作按钮样式
.moreActionsButton {
  cursor: pointer;
  padding: 4px;
}

.moreActionsIcon {
  &.normal {
    color: #999999;
  }

  &.selected {
    color: #ffffff;
  }
}
