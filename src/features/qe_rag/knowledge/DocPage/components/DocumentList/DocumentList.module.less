// 基础布局样式
.documentListCard {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.listMainContainer {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.listScrollContainer {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

// 下拉菜单项样式
.dropdownItemDisable {
  color: #faad14;
}

.dropdownItemEnable {
  color: #52c41a;
}

.dropdownItemDelete {
  color: #ff4d4f;
}

.dropdownIconMargin {
  margin-right: 8px;
}

// 分页容器样式
.paginationContainer {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

// 文档ID标签样式
.documentIdTag {
  background-color: #f5f5f5;
  color: #666;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
  margin-right: 8px;
}
